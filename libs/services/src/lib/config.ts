export const SCOPES = [
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.labels',
  'https://www.googleapis.com/auth/gmail.modify'
] as const;

export const DDJS_LABEL_PREFIX = 'DDJS/';

export const LABEL_VALUES = [
  'not_job_search_related',
  'inbound_job_opportunity',
  'thanks_for_applying_or_application_received_confirmation',
  'rejection_role_paused_or_closed',
  'rejection_role_filled',
  'rejection_other',
  'next_steps_online_assement',
  'next_steps_interview_coordination',
  'next_steps_other',
  'offer',
  'other'
] as const;

export type LabelValue = typeof LABEL_VALUES[number];

export const OPENAI = {
  MODEL: 'gpt-4o-mini',
  TEMPERATURE: 0,
  MAX_TOKENS: 5000
} as const;

// Configuration service for managing application settings
export class ConfigService {
  private static instance: ConfigService;

  private constructor() {}

  static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  /**
   * Get the user token limit from environment or default
   */
  getUserTokenLimit(): number {
    return parseInt(process.env.USER_TOKEN_LIMIT || '5000', 10);
  }

  /**
   * Get the initial token allocation for new users
   */
  getInitialTokenAllocation(): number {
    return parseInt(process.env.INITIAL_TOKEN_ALLOCATION || '5000', 10);
  }

  /**
   * Get the webapp progress webhook URL
   */
  getWebappProgressWebhookUrl(): string {
    return process.env.WEBAPP_PROGRESS_WEBHOOK_URL || 'http://localhost:3000/api/emails/progress-webhook';
  }

  /**
   * Get the maximum number of emails to process in a single batch
   */
  getMaxEmailsPerBatch(): number {
    return parseInt(process.env.MAX_EMAILS_PER_BATCH || '500', 10);
  }

  /**
   * Get the Gmail API configuration
   */
  getGmailConfig() {
    return {
      scopes: SCOPES,
      labelPrefix: DDJS_LABEL_PREFIX,
      labelValues: LABEL_VALUES
    };
  }

  /**
   * Get the OpenAI configuration
   */
  getOpenAIConfig() {
    return {
      model: process.env.OPENAI_MODEL || OPENAI.MODEL,
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || String(OPENAI.TEMPERATURE)),
      maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || String(OPENAI.MAX_TOKENS), 10)
    };
  }

  /**
   * Get database configuration
   */
  getDatabaseConfig() {
    const useProduction = process.env.USE_PRODUCTION_FIRESTORE === 'true';
    const projectId = useProduction || process.env.NODE_ENV === 'production'
      ? 'data-driven-job-search'
      : 'ddjs-dev-458016';

    return {
      projectId,
      useProduction,
      emulatorHost: process.env.FIRESTORE_EMULATOR_HOST
    };
  }

  /**
   * Check if we're running in development mode
   */
  isDevelopment(): boolean {
    return process.env.NODE_ENV === 'development';
  }

  /**
   * Check if we're running in production mode
   */
  isProduction(): boolean {
    return process.env.NODE_ENV === 'production';
  }

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig() {
    return {
      nodeEnv: process.env.NODE_ENV || 'development',
      isDevelopment: this.isDevelopment(),
      isProduction: this.isProduction(),
      logLevel: process.env.LOG_LEVEL || 'info'
    };
  }
}

export const GMAIL_CONFIG = {
  pubsub: {
    // Determine project ID based on environment
    get projectId(): string {
      const isProduction = process.env['NODE_ENV'] === 'production' ||
                          process.env['USE_PRODUCTION_FIRESTORE'] === 'true';

      return process.env['GOOGLE_CLOUD_PROJECT'] ||
             (isProduction ? 'data-driven-job-search' : 'ddjs-dev-458016');
    },
    // Use a getter for topicName to ensure it uses the current projectId
    get topicName(): string {
      const projectId = this.projectId;
      return `projects/${projectId}/topics/gmail-notifications`;
    },
    subscriptionName: 'gmail-notifications-sub'
  }
} as const;
