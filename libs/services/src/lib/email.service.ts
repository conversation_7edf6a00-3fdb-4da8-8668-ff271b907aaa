import { Database } from './database';
import { Logger } from './logger';
import { sanitizeId, sanitizeEmail } from './sanitize';
import { OpenAIService } from './openai.service';
import { TokenService } from './token.service';
import { AuthService } from './auth.service';
import { GmailService } from './gmail.service';
import { ConfigService } from './config';

interface EmailProcessingResult {
  success: number;
  failed: number;
  errors: Array<{ emailId: string; error: string }>;
  processedEmails: Array<{ id: string; analysis: any }>;
  cachedCount?: number;
  needsAnalysisCount?: number;
  remainingTokens?: number;
}

interface TokenEstimate {
  totalEmails: number;
  emailsNeedingTokens: number;
  hasEnoughTokens: boolean;
  remainingTokens: number;
}

interface StatusUpdate {
  type: 'progress' | 'complete' | 'error';
  processed: number;
  total: number;
  currentEmail?: string;
  cachedCount?: number;
  remainingTokens?: number;
  timestamp?: string;
}

type StatusCallback = (status: StatusUpdate) => void;

export class EmailService {
  private openAIService: OpenAIService;
  private tokenService: TokenService;
  private db: Database;
  private logger: Logger;
  private authService?: AuthService;
  private config: ConfigService;
  private _instanceId: string;
  private statusListeners: Map<string, StatusCallback>;

  constructor(
    openAIService: OpenAIService,
    tokenService: TokenService,
    database: Database,
    logger: Logger,
    authService?: AuthService
  ) {
    this.openAIService = openAIService;
    this.tokenService = tokenService;
    this.db = database;
    this.logger = logger;
    this.authService = authService;
    this.config = ConfigService.getInstance();

    // Create a unique instance ID for tracking purposes
    this._instanceId = `emailsvc_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
    console.log(`EmailService: New instance created with ID ${this._instanceId}`);

    // Initialize status listeners map
    this.statusListeners = new Map<string, StatusCallback>();

    if (!authService) {
      this.logger.warn('AuthService not provided to EmailService. Gmail client access will be unavailable.');
    }
  }

  addStatusListener(userId: string, callback: StatusCallback): boolean {
    if (!userId || !callback || typeof callback !== 'function') {
      console.error('EmailService: Invalid userId or callback provided');
      return false;
    }

    console.log('EmailService: Adding status listener', {
      instanceId: this._instanceId,
      userId,
      hasExistingListener: this.statusListeners.has(userId)
    });

    this.statusListeners.set(userId, callback);

    // Send an initial status update to confirm the listener is working
    this._emitStatus(userId, {
      type: 'progress',
      processed: 0,
      total: 0,
      currentEmail: `Listener connected successfully to instance ${this._instanceId}`
    });

    return true;
  }

  removeStatusListener(userId: string): boolean {
    if (!userId) {
      console.error('EmailService: Cannot remove listener without userId');
      return false;
    }

    console.log('EmailService: Removing status listener', {
      instanceId: this._instanceId,
      userId,
      hadListener: this.statusListeners.has(userId)
    });

    return this.statusListeners.delete(userId);
  }

  private _emitStatus(userId: string, status: StatusUpdate): void {
    try {
      if (!userId) {
        console.error('EmailService: Cannot emit status without userId');
        return;
      }

      const enhancedStatus: StatusUpdate = {
        ...status,
        timestamp: new Date().toISOString()
      };

      const callback = this.statusListeners.get(userId);

      if (callback && typeof callback === 'function') {
        try {
          console.log(`EmailService: Calling status callback for ${userId} from instance ${this._instanceId}`);
          callback(enhancedStatus);
        } catch (error) {
          console.error(`EmailService: Error in status callback for ${userId}`, error);
        }
      } else {
        console.warn(`EmailService: No status listener found for user ${userId}`);
      }
    } catch (error) {
      console.error(`EmailService: Error emitting status update for ${userId}`, error);
    }
  }

  private _formatDateForGmail(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}/${month}/${day}`;
  }

  private async _getAllEmailsInDateRange(
    gmailService: GmailService,
    formattedStartDate: string,
    formattedEndDate: string,
    maxEmailsToProcess?: number
  ): Promise<Array<{ id: string }>> {
    const allMessages = await gmailService.getEmailsByDateRange(formattedStartDate, formattedEndDate);

    // Get max emails from configuration
    const maxEmails = maxEmailsToProcess || this.config.getMaxEmailsPerBatch();

    // Safety limit to prevent processing too many emails at once
    if (allMessages.length > maxEmails) {
      console.log(`EmailService: Limiting to ${maxEmails} emails (found ${allMessages.length})`);
      return allMessages.slice(0, maxEmails);
    }

    return allMessages;
  }

  async estimateTokenUsage(clerkUserId: string, startDate: Date, endDate: Date): Promise<TokenEstimate> {
    try {
      this.logger.info('Estimating token usage for date range', {
        clerkUserId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      });

      if (!this.authService) {
        throw new Error('Auth service not available for getting Gmail client');
      }

      const gmailService = await this.authService.getGmailClientForUser(clerkUserId);

      // Format dates for Gmail query
      const formattedStartDate = this._formatDateForGmail(startDate);
      const formattedEndDate = this._formatDateForGmail(endDate);

      // Get emails using pagination
      const emailIds = await this._getAllEmailsInDateRange(
        gmailService,
        formattedStartDate,
        formattedEndDate
      );

      this.logger.info(`Found ${emailIds.length} emails in date range`, {
        clerkUserId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      });

      // Get the remaining tokens for the user
      const remainingTokens = await this.tokenService.getRemainingTokens(clerkUserId);
      let emailsNeedingTokens = 0;
      const totalEmails = emailIds.length;

      // Check which emails already have cached analysis
      for (const emailId of emailIds) {
        const docId = `${sanitizeId(clerkUserId)}_${sanitizeEmail(gmailService.getMonitoredEmail())}_${emailId.id}`;
        const analysisDoc = await this.db.collection('emailAnalysis')
          .doc(docId)
          .get();

        if (!analysisDoc.exists ||
            analysisDoc.data()?.['version'] !== this.openAIService.analysisVersion) {
          emailsNeedingTokens++;
        }
      }

      const hasEnoughTokens = remainingTokens >= emailsNeedingTokens;

      return {
        totalEmails,
        emailsNeedingTokens,
        hasEnoughTokens,
        remainingTokens
      };
    } catch (error) {
      this.logger.error('Failed to estimate token usage', {
        clerkUserId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error('Failed to estimate token usage');
    }
  }

  async processEmailBatch(clerkUserId: string, startDate: Date, endDate: Date): Promise<EmailProcessingResult> {
    try {
      if (!this.authService) {
        throw new Error('Auth service not available for getting Gmail client');
      }

      // Get Gmail client from auth service
      const gmailService = await this.authService.getGmailClientForUser(clerkUserId);

      // Format dates for Gmail query
      const formattedStartDate = this._formatDateForGmail(startDate);
      const formattedEndDate = this._formatDateForGmail(endDate);

      // Get all emails in the date range with pagination
      const metaMessages = await this._getAllEmailsInDateRange(
        gmailService,
        formattedStartDate,
        formattedEndDate
      );

      this.logger.info(`Processing ${metaMessages.length} emails`);

      // First, scan all emails to determine which need new analysis
      let cachedCount = 0;
      let needsAnalysisCount = 0;

      console.log(`EmailService: Pre-scanning ${metaMessages.length} emails to identify cached vs. new analysis`);

      // Check remaining tokens before processing
      const remainingTokens = await this.tokenService.getRemainingTokens(clerkUserId);

      // Pre-scan to identify cached vs. new analysis
      for (const metaMessage of metaMessages) {
        const analysisDoc = await this.db.collection('emailAnalysis')
          .doc(`${sanitizeId(clerkUserId)}_${sanitizeEmail(gmailService.getMonitoredEmail())}_${metaMessage.id}`)
          .get();

        const needsAnalysis = !analysisDoc.exists ||
          analysisDoc.data()?.['version'] !== this.openAIService.analysisVersion;

        if (needsAnalysis) {
          needsAnalysisCount++;
        } else {
          cachedCount++;
        }
      }

      // Check if we have enough tokens for analysis
      if (needsAnalysisCount > remainingTokens) {
        console.warn(`EmailService: Not enough tokens (${remainingTokens}) for email analysis. Need ${needsAnalysisCount} tokens.`);
        return {
          success: 0,
          failed: 0,
          errors: [{
            emailId: 'token-error',
            error: `Not enough tokens. You have ${remainingTokens} tokens but need ${needsAnalysisCount} for this analysis.`
          }],
          cachedCount,
          needsAnalysisCount,
          remainingTokens
        };
      }

      // Emit initial status
      this._emitStatus(clerkUserId, {
        type: 'progress',
        processed: 0,
        total: metaMessages.length,
        currentEmail: 'Starting email processing...',
        cachedCount,
        remainingTokens
      });

      // Process emails with actual analysis
      let successCount = 0;
      let failedCount = 0;
      const errors: Array<{ emailId: string; error: string }> = [];
      const processedEmails: Array<{ id: string; analysis: any }> = [];

      // Get DDJS label IDs for email labeling
      const labelIds = await gmailService.getDDJSLabelIds();

      for (let i = 0; i < metaMessages.length; i++) {
        const metaMessage = metaMessages[i];

        try {
          // Emit progress update
          this._emitStatus(clerkUserId, {
            type: 'progress',
            processed: i,
            total: metaMessages.length,
            currentEmail: `Processing email ${i + 1} of ${metaMessages.length}`,
            cachedCount,
            remainingTokens
          });

          // Check if analysis already exists (cached)
          const docId = `${clerkUserId}_${gmailService.getMonitoredEmail()}_${metaMessage.id}`;
          const analysisDoc = await this.db.collection('emailAnalysis').doc(docId).get();

          if (analysisDoc.exists && analysisDoc.data()?.version === this.openAIService.analysisVersion) {
            // Use cached analysis
            cachedCount++;
            processedEmails.push({
              id: metaMessage.id,
              analysis: analysisDoc.data()
            });
            successCount++;
            continue;
          }

          // Check if user has enough tokens for new analysis
          if (remainingTokens <= 0) {
            throw new Error('Insufficient tokens for analysis');
          }

          // Get email content
          const emailContent = await gmailService.getEmailContent(metaMessage.id);
          if (!emailContent) {
            throw new Error('Failed to retrieve email content');
          }

          // Prepare content for analysis
          const fullContent = `Subject: ${emailContent.subject}\nFrom: ${emailContent.from}\nTo: ${emailContent.to}\nDate: ${emailContent.date}\n\n${emailContent.body}`;

          // Analyze with OpenAI
          const analysis = await this.openAIService.analyzeEmail(fullContent);

          // Add appropriate label based on analysis
          if (analysis.parsed?.email_type_category) {
            const labelKey = `DDJS/${analysis.parsed.email_type_category}`;
            if (labelIds[labelKey]) {
              await gmailService.modifyMessage(metaMessage.id, {
                addLabelIds: [labelIds[labelKey]]
              });
            }
          }

          // Deduct token
          await this.tokenService.deductTokens(clerkUserId, 1);
          remainingTokens--;

          // Save analysis to database
          const analysisData = {
            ...analysis,
            date: emailContent.date,
            version: this.openAIService.analysisVersion,
            lastUpdated: new Date().toISOString()
          };

          await this.db.collection('emailAnalysis').doc(docId).set(analysisData);

          processedEmails.push({
            id: metaMessage.id,
            analysis: analysisData
          });

          successCount++;
          needsAnalysisCount++;

        } catch (error) {
          failedCount++;
          errors.push({
            emailId: metaMessage.id,
            error: error instanceof Error ? error.message : String(error)
          });

          this.logger.error('Failed to process individual email', {
            clerkUserId,
            emailId: metaMessage.id,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      // Emit completion status
      this._emitStatus(clerkUserId, {
        type: 'complete',
        processed: metaMessages.length,
        total: metaMessages.length,
        currentEmail: 'Processing complete',
        cachedCount,
        remainingTokens
      });

      return {
        success: successCount,
        failed: failedCount,
        errors,
        processedEmails,
        cachedCount,
        needsAnalysisCount: needsAnalysisCount,
        remainingTokens
      };

    } catch (error) {
      this.logger.error('Failed to process email batch', {
        clerkUserId,
        error: error instanceof Error ? error.message : String(error)
      });

      this._emitStatus(clerkUserId, {
        type: 'error',
        processed: 0,
        total: 0,
        currentEmail: 'Processing failed'
      });

      throw error;
    }
  }
}
