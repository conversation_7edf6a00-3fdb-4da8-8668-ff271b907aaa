import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { addConnection, removeConnection, sendProgress, ProgressUpdate } from '@/lib/connectionStore'
import { headers } from 'next/headers'
import { verifyToken } from '@/lib/auth'

// Set runtime to edge - crucial for streaming responses
export const runtime = 'edge'

interface AnalysisProgress {
  total: number
  processed: number
  current: string
  status: 'idle' | 'processing' | 'completed' | 'error'
  cachedCount: number
  newAnalysisCount: number
  remainingTokens?: number
}

// Define the shape of status updates from the email service
interface StatusUpdate {
  type: 'progress' | 'complete' | 'error'
  total?: number
  processed?: number
  currentEmail?: string
  remainingTokens?: number
  cachedCount?: number
  newAnalysisCount?: number
  estimate?: {
    totalEmails: number
    emailsAnalyzed: number
    emailsNeedingTokensAnalyzed: number
    remainingEmailsNeedingTokens: number
  }
}

export function GET(req: NextRequest) {
  console.log('Progress Route: GET request received')

  // --- Authentication ---
  // First try to get the user ID from the auth middleware
  // Note: In edge runtime with runtime: 'edge', auth() doesn't return a Promise
  // @ts-ignore - auth() returns { userId: string | null } directly in edge runtime
  const { userId } = auth()

  // If middleware auth fails, check for token in query params (for EventSource)
  let authenticatedUserId = userId;
  if (!authenticatedUserId) {
    // Check for token in query params
    const url = new URL(req.url);
    const tokenFromQuery = url.searchParams.get('token');

    if (tokenFromQuery) {
      try {
        console.log('Progress Route: Found token in query params, attempting to validate');

        // Verify the token using our auth utility
        const verifiedUserId = await verifyToken(tokenFromQuery);

        if (verifiedUserId) {
          authenticatedUserId = verifiedUserId;
          console.log('Progress Route: Authenticated via token in query params', { userId: verifiedUserId });
        } else {
          console.error('Progress Route: Token verification failed');
        }
      } catch (error) {
        console.error('Progress Route: Error verifying token in query params', error);
      }
    }
  }

  if (!authenticatedUserId) {
    console.error('Progress Route: Unauthorized access attempt')
    return new NextResponse('Unauthorized', { status: 401 })
  }
  console.log(`Progress Route: Authenticated user ${authenticatedUserId}`)

  // --- Check for EventSource compatibility ---
  // EventSource doesn't always send text/event-stream in accept header,
  // so we check for Sec-Fetch-Mode: cors, which is common for EventSource
  const acceptHeader = req.headers.get('accept')
  const fetchMode = req.headers.get('sec-fetch-mode')
  console.log(`Progress Route: Headers - Accept: ${acceptHeader}, Sec-Fetch-Mode: ${fetchMode}`)
  if (fetchMode !== 'cors' && acceptHeader !== 'text/event-stream') {
    console.warn('Progress Route: Request might not be from EventSource')
    // Allow connection anyway, but log it
    // return new NextResponse('This endpoint requires EventSource', { status: 400 })
  }

  // --- Create the ReadableStream ---
  const stream = new ReadableStream({
    start(controller) {
      console.log(`Progress Route: Stream started for user ${authenticatedUserId}`)

      // Store the controller for this user
      addConnection(authenticatedUserId, controller)

      // Send initial connection confirmation message
      const initialMessage: ProgressUpdate = {
        status: 'connecting',
        current: 'SSE connection established successfully.',
        processed: 0,
        total: 0
      }
      sendProgress(authenticatedUserId, initialMessage)

      // Handle client disconnection
      req.signal.onabort = () => {
        console.log(`Progress Route: Client disconnected for user ${authenticatedUserId}. Abort signal received.`)
        removeConnection(authenticatedUserId)
        try {
          controller.close() // Ensure the stream controller is closed
        } catch (e) {
          console.error(`Progress Route: Error closing controller on abort for user ${authenticatedUserId}:`, e)
        }
      }

      // Keep connection alive with pings (optional but good practice)
      const pingInterval = setInterval(() => {
        try {
          // Send a comment line as a ping
          controller.enqueue(new TextEncoder().encode(': ping\n\n'))
          console.log(`Progress Route: Ping sent to user ${authenticatedUserId}`)
        } catch (error) {
          console.error(`Progress Route: Error sending ping to user ${authenticatedUserId}, connection likely closed:`, error)
          removeConnection(authenticatedUserId) // Remove connection if ping fails
          clearInterval(pingInterval)
          try {
            controller.close()
          } catch (e) {
            console.error(`Progress Route: Error closing controller after ping fail for user ${authenticatedUserId}:`, e)
          }
        }
      }, 30000) // Send a ping every 30 seconds

      // Ensure interval is cleared when connection ends
      req.signal.onabort = () => {
        console.log(`Progress Route: Connection aborted for user ${authenticatedUserId}. Cleaning up.`)
        clearInterval(pingInterval)
        removeConnection(authenticatedUserId)
        try {
          controller.close()
        } catch (e) {
          console.error(`Progress Route: Error closing controller on abort:`, e)
        }
      }
    },
    cancel(reason) {
      console.log(`Progress Route: Stream cancelled for user ${authenticatedUserId}. Reason:`, reason)
      // This might be called if the stream is cancelled programmatically
      removeConnection(authenticatedUserId)
    },
  })

  // Return the stream as the response
  console.log(`Progress Route: Returning stream response for user ${authenticatedUserId}`)
  return new NextResponse(stream, {
    status: 200,
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      'Connection': 'keep-alive',
      // Optional: Add CORS headers if needed, though usually handled by framework
      // 'Access-Control-Allow-Origin': '*',
    },
  })
}