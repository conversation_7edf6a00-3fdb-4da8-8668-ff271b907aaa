import { NextRequest, NextResponse } from 'next/server'

// This route provides information about the Socket.IO server
// The actual Socket.IO server is initialized in server.js and handles requests at /api/socket
export async function GET(request: NextRequest) {
  try {
    // Check if we can access the global Socket.IO instance
    const hasSocketIO = typeof global !== 'undefined' && global.io !== undefined;

    return NextResponse.json({
      message: 'Socket.IO server status',
      status: hasSocketIO ? 'active' : 'inactive',
      path: '/api/socket',
      info: hasSocketIO
        ? 'Socket.IO server is running and ready for connections'
        : 'Socket.IO server is not initialized. Please ensure the custom server is running.',
      connectionInfo: {
        endpoint: '/api/socket',
        authRequired: true,
        supportedEvents: ['progress', 'connect', 'disconnect']
      }
    })
  } catch (error) {
    return NextResponse.json({
      message: 'Socket.IO server status check failed',
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  // POST requests to this endpoint are not supported
  // Socket.IO uses its own protocol for real-time communication
  return NextResponse.json({
    message: 'Socket.IO communication',
    status: 'not_supported',
    note: 'Socket.IO uses its own protocol. Use the Socket.IO client library to connect.',
    endpoint: '/api/socket'
  }, { status: 405 })
}
