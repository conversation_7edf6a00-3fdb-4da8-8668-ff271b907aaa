// GCP Cloud Functions 2nd Gen imports with functions-framework
const functions = require('@google-cloud/functions-framework');
import { PubSub } from '@google-cloud/pubsub';
import { Database, Logger } from '../../../libs/services/src/index';

// Initialize services
const logger = new Logger();
const database = new Database();
const pubsub = new PubSub();

// Gmail notification handler function (HTTP triggered)
functions.http('handleGmailNotification', async (req: any, res: any) => {
  try {
    // Verify this is a POST request
    if (req.method !== 'POST') {
      res.status(405).send('Method Not Allowed');
      return;
    }

    // Parse the Pub/Sub message from Gmail
    const message = req.body.message;
    if (!message || !message.data) {
      logger.warn('Invalid notification format received');
      res.status(400).send('Invalid notification format');
      return;
    }

    // Decode the message data
    const decodedData = Buffer.from(message.data, 'base64').toString();
    const notificationData = JSON.parse(decodedData);

    logger.info('Received Gmail notification', {
      historyId: notificationData.historyId,
      emailAddress: notificationData.emailAddress
    });

    // Get user ID from email address mapping
    const userMappingDoc = await database.collection('emailUserMapping')
      .doc(notificationData.emailAddress.replace(/[^a-zA-Z0-9@._-]/g, '_'))
      .get();

    if (!userMappingDoc.exists) {
      logger.warn('No user mapping found for email address', {
        emailAddress: notificationData.emailAddress
      });
      res.status(200).send('No user mapping found');
      return;
    }

    const { clerkUserId } = userMappingDoc.data() as { clerkUserId: string };

    // Get user preferences
    const preferencesDoc = await database.collection('notificationPreferences')
      .doc(clerkUserId)
      .get();

    const preferences = preferencesDoc.exists ? preferencesDoc.data() : {
      automaticAnalysis: false,
      skipInbox: {
        confirmations: false,
        rejections: false
      }
    };

    if (!preferences.automaticAnalysis) {
      logger.info('Automatic analysis disabled for user', { clerkUserId });
      res.status(200).send('Automatic analysis disabled');
      return;
    }

    // Process Gmail history to get actual new message IDs
    try {
      const authService = container.getAuthService();
      const gmailService = await authService.getGmailClientForUser(clerkUserId);

      // Get the user's last processed history ID from database
      const userDoc = await database.collection('users').doc(clerkUserId).get();
      const lastHistoryId = userDoc.exists ? userDoc.data()?.lastProcessedHistoryId : null;

      logger.info('Processing Gmail history', {
        clerkUserId,
        currentHistoryId: notificationData.historyId,
        lastProcessedHistoryId: lastHistoryId
      });

      // Get history changes since last processed history ID
      const historyResponse = await gmailService.getHistory(
        lastHistoryId || notificationData.historyId,
        notificationData.historyId
      );

      const newMessageIds: string[] = [];

      if (historyResponse && historyResponse.history) {
        for (const historyRecord of historyResponse.history) {
          if (historyRecord.messagesAdded) {
            for (const messageAdded of historyRecord.messagesAdded) {
              if (messageAdded.message?.id) {
                newMessageIds.push(messageAdded.message.id);
              }
            }
          }
        }
      }

      logger.info('Found new messages in history', {
        clerkUserId,
        newMessageCount: newMessageIds.length,
        messageIds: newMessageIds.slice(0, 5) // Log first 5 for debugging
      });

      // Publish analysis requests for each new message
      for (const messageId of newMessageIds) {
        const emailAnalysisMessage = {
          clerkUserId,
          messageId,
          monitoredEmail: notificationData.emailAddress,
          historyId: notificationData.historyId
        };

        await pubsub.topic('email-analysis-requests').publishMessage({
          data: Buffer.from(JSON.stringify(emailAnalysisMessage))
        });
      }

      // Update the user's last processed history ID
      await database.collection('users').doc(clerkUserId).set({
        lastProcessedHistoryId: notificationData.historyId,
        lastNotificationProcessedAt: new Date().toISOString()
      }, { merge: true });

      logger.info('Email analysis requests published', {
        clerkUserId,
        historyId: notificationData.historyId,
        messagesPublished: newMessageIds.length
      });

    } catch (historyError) {
      logger.error('Failed to process Gmail history, falling back to single message', {
        clerkUserId,
        historyId: notificationData.historyId,
        error: historyError instanceof Error ? historyError.message : String(historyError)
      });

      // Fallback: publish a single analysis request with the history ID as message ID
      const fallbackMessage = {
        clerkUserId,
        messageId: `fallback_${notificationData.historyId}`,
        monitoredEmail: notificationData.emailAddress,
        historyId: notificationData.historyId
      };

      await pubsub.topic('email-analysis-requests').publishMessage({
        data: Buffer.from(JSON.stringify(fallbackMessage))
      });

      logger.info('Fallback email analysis request published', {
        clerkUserId,
        historyId: notificationData.historyId
      });
    }

    res.status(200).send('OK');

  } catch (error) {
    logger.error('Failed to process Gmail notification', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    res.status(500).send('Internal Server Error');
  }
});
